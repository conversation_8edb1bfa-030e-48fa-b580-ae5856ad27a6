import type { Tables } from '$lib/database.types';
import type { UpdateBudgetItem } from './schemas/project';
import { stratify, type HierarchyNode } from 'd3-hierarchy';
import { interpolateCool as interpolateGnBu } from 'd3-scale-chromatic';
import { hsl } from 'd3-color';

// 1) Raw data shape coming from supabase.from('wbs_library_item').select(...)
export interface RawNode extends Tables<'wbs_library_item'> {
	budget_line_item_current: Tables<'budget_line_item_current'>[];
}

// Budget snapshot related interfaces
export interface BudgetSnapshotLineItem extends Tables<'budget_snapshot_line_item'> {
	wbs_library_item?: Tables<'wbs_library_item'>;
}

export interface BudgetSnapshot extends Tables<'budget_snapshot'> {
	project_stage?: {
		name: string;
		stage_order: number;
		stage: number | null;
		project_id: string;
	};
}

// 2) Node augmented with its computed costs and children
export interface NodeWithCosts extends RawNode {
	children: NodeWithCosts[];
	directCost: number;
	childrenCost: number;
	totalCost: number;
}

export interface EnhancedWbsItemTree extends Tables<'wbs_library_item'> {
	budgetItems: RawNode['budget_line_item_current'];
	directCost: number;
	childrenCost: number;
	totalCost: number;
	children: EnhancedWbsItemTree[];
	nodeId: string; // e.g. `node-${wbs_library_item_id}`
}

// Base interface for budget line items with common properties
export interface BudgetLineItemBase {
	wbs_library_item_id: string;
	quantity: number | null;
	unit_rate: number | null;
	factor: number | null;
	label?: string; // Optional label to distinguish between different budget versions (e.g., 'current', 'snapshot-stage-1')
}

// Generic interface for WBS items with budget data
export interface WbsItemWithBudgetData<T extends BudgetLineItemBase>
	extends Tables<'wbs_library_item'> {
	budgetData?: T;
	subtotal: number;
	value: number; // Used by stratify for automatic totaling
	totalFactor: number; // Cumulative factor from current item and all ancestors
}

export interface BudgetHierarchyNode<
	T extends BudgetLineItemBase = Tables<'budget_snapshot_line_item'>,
> {
	id: string;
	parentId: string | null;
	data: WbsItemWithBudgetData<T>;
	value: number;
	children?: BudgetHierarchyNode<T>[];
	depth: number;
}

export function buildBudgetTree(raw: RawNode[]): EnhancedWbsItemTree[] {
	// A) Clone each raw node into an EnhancedWbsItemTree skeleton
	const map = new Map<string, EnhancedWbsItemTree>();
	raw.forEach((r) => {
		map.set(r.wbs_library_item_id, {
			...r, // all wbs_library_item columns
			budgetItems: r.budget_line_item_current,
			directCost: 0,
			childrenCost: 0,
			totalCost: 0,
			children: [],
			nodeId: `node-${r.wbs_library_item_id}`,
		});
	});

	// B) Hook up parent ↔ child
	const roots: EnhancedWbsItemTree[] = [];
	map.forEach((node) => {
		if (node.parent_item_id && map.has(node.parent_item_id)) {
			map.get(node.parent_item_id)!.children.push(node);
		} else {
			roots.push(node);
		}
	});

	// C) Compute costs bottom-up
	function computeCosts(node: EnhancedWbsItemTree) {
		// directCost = sum(quantity * unit_rate)
		node.directCost = node.budgetItems.reduce(
			(sum, bi) => sum + bi.quantity * bi.unit_rate * (bi.factor ?? 1),
			0,
		);

		// first compute children, accumulate their totalCost
		node.children.forEach((child) => {
			computeCosts(child);
			node.childrenCost += child.totalCost;
		});

		// total = direct + children
		node.totalCost = node.directCost + node.childrenCost;
	}
	roots.forEach(computeCosts);

	return roots;
}

export function calculateUnitRate(
	item: Partial<RawNode['budget_line_item_current'][number]>,
): number {
	if (item.unit_rate_manual_override) {
		if (!item.unit_rate) {
			item.unit_rate = 0;
		}
		// If unit rate is manually overridden, return the manual value
		return item.unit_rate;
	}

	const materialCost = item.material_rate || 0;
	const laborCost = item.labor_rate || 0;
	const productivity = item.productivity_per_hour || 0;

	// If productivity is provided, calculate labor cost per unit
	const laborCostPerUnit = productivity > 0 ? laborCost / productivity : 0;

	return materialCost + laborCostPerUnit;
}

/**
 * Calculate subtotal for a budget item using the formula:
 * subtotal = quantity * unit_rate * (factor ?? 1)
 */
export function calculateSubtotal(
	snapshotData: Tables<'budget_snapshot_line_item'> | undefined,
): number {
	if (!snapshotData) return 0;
	return (snapshotData.quantity || 0) * (snapshotData.unit_rate || 0) * (snapshotData.factor ?? 1);
}

/**
 * Generic calculate subtotal for any budget line item type using the formula:
 * subtotal = quantity * unit_rate * (factor ?? 1)
 */
export function calculateBudgetItemSubtotal<T extends BudgetLineItemBase>(
	budgetItem: T | undefined,
): number {
	if (!budgetItem) return 0;
	return (budgetItem.quantity || 0) * (budgetItem.unit_rate || 0) * (budgetItem.factor ?? 1);
}

/**
 * Calculate total factors for all WBS items, considering hierarchical factor inheritance
 */
function calculateTotalFactors<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItemMap: Map<string, T>,
): Map<string, number> {
	const totalFactorMap = new Map<string, number>();

	// Create a map of children by parent for efficient lookup
	const childrenMap = new Map<string, Tables<'wbs_library_item'>[]>();
	wbsItems.forEach((item) => {
		const parentId = item.parent_item_id;
		if (parentId) {
			if (!childrenMap.has(parentId)) {
				childrenMap.set(parentId, []);
			}
			childrenMap.get(parentId)!.push(item);
		}
	});

	// Recursive function to calculate total factor for an item
	function calculateTotalFactor(item: Tables<'wbs_library_item'>): number {
		// Check if already calculated
		if (totalFactorMap.has(item.wbs_library_item_id)) {
			return totalFactorMap.get(item.wbs_library_item_id)!;
		}

		// Get the item's own factor from budget data
		const budgetData = budgetItemMap.get(item.wbs_library_item_id);
		const itemFactor = budgetData?.factor ?? 1;

		// Calculate parent's total factor
		let parentTotalFactor = 1;
		if (item.parent_item_id) {
			const parentItem = wbsItems.find((w) => w.wbs_library_item_id === item.parent_item_id);
			if (parentItem) {
				parentTotalFactor = calculateTotalFactor(parentItem);
			}
		}

		// Total factor is parent's total factor multiplied by this item's factor
		const totalFactor = parentTotalFactor * itemFactor;
		totalFactorMap.set(item.wbs_library_item_id, totalFactor);

		return totalFactor;
	}

	// Calculate total factors for all items
	wbsItems.forEach((item) => calculateTotalFactor(item));

	return totalFactorMap;
}

/**
 * Create hierarchical budget structure using stratify - generic version
 */
export function createGenericBudgetHierarchy<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItems: T[],
) {
	// Create a map of budget data by WBS item ID
	const budgetItemMap = new Map<string, T>();
	budgetItems.forEach((item) => {
		budgetItemMap.set(item.wbs_library_item_id, item);
	});

	// Calculate total factors for all items
	const totalFactorMap = calculateTotalFactors(wbsItems, budgetItemMap);

	// Transform WBS items to include budget data and calculated values
	const itemsWithData: WbsItemWithBudgetData<T>[] = wbsItems.map((item) => {
		const budgetData = budgetItemMap.get(item.wbs_library_item_id);
		const subtotal = calculateBudgetItemSubtotal(budgetData);
		const totalFactor = totalFactorMap.get(item.wbs_library_item_id) ?? 1;

		// For stratify value calculation:
		// - If item has budget data: use quantity * unit_rate (without factor)
		// - The totalFactor will be applied during stratify summation
		let value = 0;
		if (budgetData) {
			value = (budgetData.quantity || 0) * (budgetData.unit_rate || 0);
		}

		return {
			...item,
			budgetData,
			subtotal,
			value,
			totalFactor,
		};
	});

	// Create a project budget root node to contain all WBS items
	const projectBudgetRoot: WbsItemWithBudgetData<T> = {
		wbs_library_item_id: '__project_budget_root__',
		wbs_library_id: '__project_budget_root__',
		level: 0,
		in_level_code: '0',
		parent_item_id: null,
		code: '0',
		description: 'Project Budget Total',
		cost_scope: null,
		item_type: 'Standard',
		client_id: null,
		project_id: null,
		created_at: '',
		updated_at: '',
		budgetData: undefined,
		subtotal: 0,
		value: 0,
		totalFactor: 1,
	};

	// how many root items are there?
	const rootItems = itemsWithData.reduce(
		(count, item) => (!item.parent_item_id ? count + 1 : count),
		0,
	);

	// Add project budget root and update parent references for root items
	const allItems =
		rootItems > 1
			? [
					projectBudgetRoot,
					...itemsWithData.map((item) => ({
						...item,
						parent_item_id: item.parent_item_id || '__project_budget_root__',
					})),
				]
			: itemsWithData;

	// Use stratify to create hierarchical structure with proper value calculation
	const stratifyFn = stratify<WbsItemWithBudgetData<T>>()
		.id((d) => d.wbs_library_item_id)
		.parentId((d) => d.parent_item_id);

	// Create the hierarchy and apply sum calculation that accounts for totalFactor
	const root = stratifyFn(allItems).sum((d) => {
		// For items with budget data, use base value multiplied by totalFactor
		if (d.budgetData) {
			return d.value * d.totalFactor;
		}
		// For items without budget data (categories), return 0 so they don't contribute to sum
		return 0;
	});

	// Fix subtotal calculation for category items after hierarchy is built
	function fixCategorySubtotals(node: HierarchyNode<WbsItemWithBudgetData<T>>): void {
		// If this is a category item (has children but no budget data or has budget data with no quantity/rate)
		if (node.children && node.children.length > 0) {
			const hasOwnBudgetData =
				node.data.budgetData && (node.data.budgetData.quantity || node.data.budgetData.unit_rate);

			if (!hasOwnBudgetData) {
				// Calculate sum of children's subtotals
				const childrenSum = node.children.reduce(
					(sum: number, child) => sum + child.data.subtotal,
					0,
				);

				// Apply this item's own factor (if any) to the children sum
				const ownFactor = node.data.budgetData?.factor ?? 1;
				node.data.subtotal = childrenSum * ownFactor;
			}
		}

		// Recursively fix children
		if (node.children) {
			node.children.forEach(fixCategorySubtotals);
		}
	}

	// Apply the fix to the entire hierarchy
	fixCategorySubtotals(root);

	return root;
}

/**
 * Create hierarchical budget structure for current budget line items
 */
export function createCurrentBudgetHierarchy(
	wbsItems: Tables<'wbs_library_item'>[],
	currentItems: Tables<'budget_line_item_current'>[],
) {
	return createGenericBudgetHierarchy(wbsItems, currentItems);
}

/**
 * Create hierarchical budget structure for budget snapshots using the generic version
 */
export function createSnapshotBudgetHierarchy(
	wbsItems: Tables<'wbs_library_item'>[],
	snapshotItems: Tables<'budget_snapshot_line_item'>[],
) {
	return createGenericBudgetHierarchy(wbsItems, snapshotItems);
}

export type SnapshotBudgetNode = ReturnType<
	typeof createGenericBudgetHierarchy<Tables<'budget_snapshot_line_item'>>
>;
export type CurrentBudgetNode = ReturnType<typeof createCurrentBudgetHierarchy>;

// Union type for budget hierarchy nodes that can handle both current and snapshot data
export type UnifiedBudgetNode = SnapshotBudgetNode | CurrentBudgetNode;

/**
 * Interface for budget data at a specific depth level
 */
export interface BudgetDataForDepth {
	wbsCode: string;
	wbsDescription: string;
	totalValue: number;
	label: string; // Used for chart x-axis labels to distinguish between budget versions
}

/**
 * Create budget data for stacked bar chart visualization at a specific WBS code depth
 *
 * @param wbsItems - Array of WBS library items
 * @param budgetItems - Array of budget line items (current or snapshot)
 * @param targetDepth - The WBS code depth level to filter for (1-based, determined by number of dots in WBS code)
 *                     e.g., depth 1 = "01", depth 2 = "01.01", depth 3 = "01.01.01"
 * @param label - Label to identify this budget version (e.g., 'current', 'snapshot-stage-1')
 * @returns Array of budget data objects for the specified depth level
 */
export function createBudgetDataForDepth<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItems: T[],
	targetDepth: number,
	label: string,
): BudgetDataForDepth[] {
	// First create the full hierarchy to get proper value calculations
	const hierarchy = createGenericBudgetHierarchy(wbsItems, budgetItems);

	// Helper function to calculate WBS depth based on code structure
	function getWbsDepth(code: string): number {
		// Count the number of dots in the WBS code to determine depth
		// e.g., "01" = depth 1, "01.01" = depth 2, "01.01.01" = depth 3
		return code.split('.').length;
	}

	// Helper function to get parent WBS code
	function getParentCode(code: string): string | null {
		const parts = code.split('.');
		if (parts.length <= 1) return null;
		return parts.slice(0, -1).join('.');
	}

	// Helper function to traverse hierarchy and collect items at target depth with fallback
	function collectItemsAtDepth(
		node: HierarchyNode<WbsItemWithBudgetData<T>>,
		results: BudgetDataForDepth[] = [],
		coveredBranches: Set<string> = new Set(),
	): BudgetDataForDepth[] {
		// Skip the artificial project budget root
		if (node.data.wbs_library_item_id === '__project_budget_root__') {
			// For project budget root, just traverse children
			if (node.children) {
				node.children.forEach((child) => {
					collectItemsAtDepth(child, results, coveredBranches);
				});
			}
		} else {
			const wbsDepth = getWbsDepth(node.data.code);

			// Check if this node is at the target depth (using WBS code structure)
			if (wbsDepth === targetDepth) {
				// Check if this branch hasn't been covered by a fallback from a parent
				if (!isBranchCovered(node.data.code, coveredBranches)) {
					results.push({
						wbsCode: node.data.code,
						wbsDescription: node.data.description,
						label: label,
						totalValue: node.value || 0, // Use the calculated value from stratify
					});
					// Mark this branch as covered
					markBranchAsCovered(node.data.code, coveredBranches);
				}
			} else if (wbsDepth < targetDepth) {
				// This node is above target depth - check if it should be used as fallback
				// Only use as fallback if:
				// 1. It has non-zero budget value
				// 2. None of its descendants at target depth have budget data
				// 3. This branch hasn't been covered yet
				if ((node.value || 0) > 0 && !isBranchCovered(node.data.code, coveredBranches)) {
					const hasDescendantsAtTargetDepth = hasDescendantsWithBudgetAtDepth(node, targetDepth);

					if (!hasDescendantsAtTargetDepth) {
						// Use this node as fallback for the target depth
						results.push({
							wbsCode: node.data.code,
							wbsDescription: node.data.description,
							label: label,
							totalValue: node.value || 0,
						});
						// Mark this entire branch as covered to prevent descendants from being included
						markBranchAsCovered(node.data.code, coveredBranches);
						return results; // Don't traverse children since we're using this as fallback
					}
				}
			}

			// Continue traversing children if we haven't used this node as fallback
			if (node.children && !isBranchCovered(node.data.code, coveredBranches)) {
				node.children.forEach((child) => {
					collectItemsAtDepth(child, results, coveredBranches);
				});
			}
		}

		return results;
	}

	// Helper function to check if a branch is already covered by a parent fallback
	function isBranchCovered(code: string, coveredBranches: Set<string>): boolean {
		// Check if this code or any of its parents are in the covered set
		let currentCode: string | null = code;
		while (currentCode) {
			if (coveredBranches.has(currentCode)) {
				return true;
			}
			currentCode = getParentCode(currentCode);
		}
		return false;
	}

	// Helper function to mark a branch as covered
	function markBranchAsCovered(code: string, coveredBranches: Set<string>): void {
		coveredBranches.add(code);
	}

	// Helper function to check if a node has descendants with budget data at target depth
	function hasDescendantsWithBudgetAtDepth(
		node: HierarchyNode<WbsItemWithBudgetData<T>>,
		targetDepth: number,
	): boolean {
		if (!node.children) return false;

		for (const child of node.children) {
			const childDepth = getWbsDepth(child.data.code);

			if (childDepth === targetDepth && (child.value || 0) > 0) {
				return true;
			}

			if (childDepth < targetDepth && hasDescendantsWithBudgetAtDepth(child, targetDepth)) {
				return true;
			}
		}

		return false;
	}

	// Start traversal from the root
	return collectItemsAtDepth(hierarchy);
}

/**
 * Interface for stacked bar chart data
 */
export interface StackedBudgetData {
	snapshot: string;
	[wbsCode: string]: string | number;
}

/**
 * Create budget data for stacked bar chart visualization from multiple snapshots
 *
 * @param wbsItems - Array of WBS library items
 * @param snapshotsData - Array of objects containing snapshot info and budget items
 * @param targetDepth - The WBS code depth level to filter for (1-based)
 * @returns Array of objects suitable for stacked bar chart, where each object represents one snapshot
 */
export function createStackedBudgetData<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	snapshotsData: Array<{
		snapshot:
			| BudgetSnapshot
			| {
					budget_snapshot_id: string;
					project_stage_id: string;
					freeze_date: string;
					freeze_reason: string | null;
					created_by_user_id: string;
					created_at: string;
					updated_at: string;
					project_stage?: {
						name: string;
						stage_order: number;
						stage: number | null;
						project_id: string;
					};
			  };
		budgetItems: T[];
	}>,
	targetDepth: number,
): StackedBudgetData[] {
	// Get all unique WBS codes at the target depth across all snapshots
	const allWbsCodes = new Set<string>();

	// First pass: collect all WBS codes at target depth from all snapshots
	snapshotsData.forEach(({ budgetItems }) => {
		const depthData = createBudgetDataForDepth(wbsItems, budgetItems, targetDepth, 'temp');
		depthData.forEach((item) => {
			if (item.totalValue !== 0) {
				// Only include non-zero values
				allWbsCodes.add(item.wbsCode);
			}
		});
	});

	// Convert to sorted array for consistent ordering
	const sortedWbsCodes = Array.from(allWbsCodes).sort((a, b) =>
		a.localeCompare(b, undefined, { numeric: true }),
	);

	// Second pass: create stacked data for each snapshot
	const stackedData: StackedBudgetData[] = snapshotsData.map(({ snapshot, budgetItems }) => {
		const depthData = createBudgetDataForDepth(wbsItems, budgetItems, targetDepth, 'temp');

		// Create a map for quick lookup
		const valueMap = new Map<string, number>();
		depthData.forEach((item) => {
			valueMap.set(item.wbsCode, item.totalValue);
		});

		// Create the stacked data object
		const stackedItem: StackedBudgetData = {
			snapshot:
				snapshot.project_stage?.name || `Stage ${snapshot.project_stage?.stage_order || 'Unknown'}`,
		};

		// Add values for each WBS code (0 if not present in this snapshot)
		sortedWbsCodes.forEach((wbsCode) => {
			stackedItem[wbsCode] = Math.round(valueMap.get(wbsCode) || 0);
		});

		return stackedItem;
	});

	return stackedData;
}

/**
 * Generate hierarchical colors for WBS codes based on their structure and depth
 *
 * @param wbsCodes - Array of WBS codes to generate colors for
 * @param wbsItems - Array of WBS library items for structure information
 * @returns Map of WBS codes to their assigned colors
 */
export function generateWbsColors(wbsCodes: string[]): Map<string, string> {
	// generate all parents
	const parentSet = new Set<string>();
	wbsCodes.forEach((code) => {
		let parentCode = getParentCode(code);
		while (parentCode) {
			parentSet.add(parentCode);
			parentCode = getParentCode(parentCode);
		}
	});

	// count level 2 codes - codes that have 1 dot
	const level2CodeCount = [...parentSet].filter((code) => code.split('.').length === 2).length;

	const colorMap = new Map<string, string>();

	// Helper function to get WBS depth based on code structure
	function getWbsDepth(code: string): number {
		return code.split('.').length;
	}

	// Helper function to get parent WBS code
	function getParentCode(code: string): string | null {
		const parts = code.split('.');
		if (parts.length <= 1) return null;
		return parts.slice(0, -1).join('.');
	}

	// Group codes by depth level
	const codesByDepth = new Map<number, string[]>();
	[...wbsCodes, ...parentSet].forEach((code) => {
		const depth = getWbsDepth(code);
		if (!codesByDepth.has(depth)) {
			codesByDepth.set(depth, []);
		}
		codesByDepth.get(depth)!.push(code);
	});

	// Sort depth levels
	const sortedDepths = Array.from(codesByDepth.keys()).sort((a, b) => a - b);

	// Process each depth level
	sortedDepths.forEach((depth) => {
		const codesAtDepth = codesByDepth.get(depth)!;

		if (depth === 1) {
			// Level 1: Use CSS primary color
			codesAtDepth.forEach((code) => {
				colorMap.set(code, interpolateGnBu(0.5));
			});
		} else if (depth === 2) {
			// Level 2: Use d3.interpolateCool for distinct colors
			codesAtDepth.forEach((code, index) => {
				const colorValue = codesAtDepth.length > 1 ? (index + 0.5) / codesAtDepth.length : 0.5;
				colorMap.set(code, interpolateGnBu(colorValue));
			});
		} else {
			// Level 3+: Use parent color with modified opacity/lightness
			codesAtDepth.forEach((code) => {
				const parentCode = getParentCode(code);
				if (parentCode && colorMap.has(parentCode)) {
					const parentColor = colorMap.get(parentCode)!;

					// Find siblings at this level with the same parent
					const siblings = codesAtDepth.filter((c) => getParentCode(c) === parentCode);
					const siblingIndex = siblings.indexOf(code);
					const siblingCount = siblings.length;

					// Modify the parent color based on sibling position
					let modifiedColor: string;

					if (parentColor.startsWith('hsl(var(--primary))')) {
						// For primary color, create variations using opacity
						const opacity =
							siblingCount > 1 ? 0.4 + (0.6 * siblingIndex) / (siblingCount - 1) : 0.7;
						modifiedColor = `hsla(var(--primary), ${opacity})`;
					} else {
						// For other colors, parse and modify lightness/saturation
						try {
							const parsedColor = hsl(parentColor);
							if (parsedColor) {
								// Vary lightness based on sibling position
								const lightnessVariation =
									siblingCount > 1 ? 0.2 + (0.6 * siblingIndex) / (siblingCount - 1) : 0.5;
								parsedColor.l = Math.max(0.1, Math.min(0.9, lightnessVariation));
								modifiedColor = parsedColor.toString();
							} else {
								// Fallback to parent color with opacity
								const opacity =
									siblingCount > 1 ? 0.4 + (0.6 * siblingIndex) / (siblingCount - 1) : 0.7;
								modifiedColor = parentColor.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
							}
						} catch {
							// Fallback to parent color with opacity
							const opacity =
								siblingCount > 1 ? 0.4 + (0.6 * siblingIndex) / (siblingCount - 1) : 0.7;
							modifiedColor = parentColor.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
						}
					}

					colorMap.set(code, modifiedColor);
				} else {
					// Fallback: use interpolateTurbo if parent not found
					const fallbackIndex = codesAtDepth.indexOf(code);
					const colorValue =
						codesAtDepth.length > 1 ? fallbackIndex / (codesAtDepth.length - 1) : 0;
					colorMap.set(code, interpolateGnBu(colorValue));
				}
			});
		}
	});

	return colorMap;
}

/**
 * Create chart configuration for stacked budget chart
 *
 * @param wbsCodes - Array of WBS codes that will be used as stack components
 * @param wbsItems - Array of WBS library items for descriptions
 * @returns ChartConfig object for use with the chart component
 */
export function createStackedBudgetChartConfig(
	wbsCodes: string[],
	wbsItems: Tables<'wbs_library_item'>[],
): Record<string, { label: string; color: string }> {
	// Create a map for quick WBS item lookup
	const wbsMap = new Map<string, Tables<'wbs_library_item'>>();
	wbsItems.forEach((item) => {
		wbsMap.set(item.code, item);
	});

	// Generate hierarchical colors based on WBS structure
	const colorMap = generateWbsColors(wbsCodes);

	const config: Record<string, { label: string; color: string }> = {};

	wbsCodes.forEach((code) => {
		const wbsItem = wbsMap.get(code);
		const description = wbsItem?.description || code;

		// Create a shorter label for the legend
		const label =
			description.length > 20
				? `${code}: ${description.slice(0, 15)}...`
				: `${code}: ${description}`;

		config[code] = {
			label,
			color: colorMap.get(code) || interpolateGnBu(0), // Fallback to interpolateCool if color not found
		};
	});

	return config;
}

// Enhanced BudgetLineItem with UI state
export interface BudgetLineItem extends Partial<UpdateBudgetItem> {
	budget_line_item_id?: string;
	project_id: string;
	wbs_library_item_id: string;
	quantity: number;
	unit: string | null;
	material_rate: number;
	labor_rate: number | null;
	productivity_per_hour: number | null;
	unit_rate_manual_override: boolean;
	unit_rate: number;
	remarks: string | null;
	cost_certainty: number | null;
	design_certainty: number | null;
	created_at?: string;
	updated_at?: string;
	// UI-specific fields
	wbs_code?: string;
	description?: string;
	subtotal?: number;
	extension?: number;
	isParent?: boolean;
	level?: number;
}
